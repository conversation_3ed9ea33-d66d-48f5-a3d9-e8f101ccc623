// Mock Authentication Service
// This provides fallback authentication when API is not available

export interface MockUser {
  id: string;
  name: string;
  email: string;
  role: 'retail_director' | 'team_leader' | 'employee';
  team_id: string;
  location: string;
  department: string;
  department_type: string;
  position: string;
  status: string;
  password_changed: boolean;
  avatar?: string;
}

export interface MockTeam {
  id: string;
  name: string;
  leader_id: string;
  location: string;
  description: string;
  department: string;
  department_type: string;
}

// Mock users data - chỉ giữ lại một số users cần thiết cho fallback
export const mockUsers: MockUser[] = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    role: 'retail_director',
    team_id: '1',
    location: 'Hà Nội',
    department: 'Bán lẻ',
    department_type: 'retail',
    position: 'Trưởng phòng',
    status: 'active',
    password_changed: true,
  },
  {
    id: '1b',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    role: 'retail_director',
    team_id: '1',
    location: '<PERSON><PERSON> Nội',
    department: 'Bán lẻ',
    department_type: 'retail',
    position: 'Trưởng phòng',
    status: 'active',
    password_changed: true,
  },
];

// Danh sách emails thật từ hệ thống - tất cả đều có thể đăng nhập với password 123456
export const realUserEmails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

// Email aliases cho dễ nhớ
export const emailAliases: Record<string, string> = {
  // Khổng Đức Mạnh
  'ducmanh': '<EMAIL>',
  'manh': '<EMAIL>',

  // Lương Việt Anh
  'vietanh': '<EMAIL>',
  'anh': '<EMAIL>',

  // Lê Khánh Duy
  'khanhduy': '<EMAIL>',
  'duy': '<EMAIL>',

  // Nguyễn Thị Thảo
  'thao': '<EMAIL>',

  // Nguyễn Mạnh Linh
  'manhlinh': '<EMAIL>',
  'linh': '<EMAIL>',

  // Trịnh Thị Bốn
  'bon': '<EMAIL>',

  // Phạm Thị Hương
  'huong': '<EMAIL>',

  // Nguyễn Thị Nga
  'nga': '<EMAIL>',

  // Hà Nguyễn Thanh Tuyền
  'tuyen': '<EMAIL>',

  // Nguyễn Ngọc Việt Khanh
  'vietkhanh': '<EMAIL>',
  'khanh': '<EMAIL>',

  // Phùng Thị Thuỳ Vân
  'thuyvan': '<EMAIL>',
  'van': '<EMAIL>',
};

// Mock teams data - sẽ sử dụng teams từ API/Firebase thay vì mock
export const mockTeams: MockTeam[] = [
  {
    id: '1',
    name: 'Nhóm Bán lẻ Hà Nội',
    leader_id: '1',
    location: 'Hà Nội',
    description: 'Nhóm phụ trách bán lẻ khu vực Hà Nội',
    department: 'Bán lẻ',
    department_type: 'retail',
  },
];

// Mock authentication function - chấp nhận tất cả emails thật từ hệ thống
export const mockLogin = async (email: string, password: string): Promise<{
  success: boolean;
  data?: { user: MockUser; token: string };
  error?: string;
}> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Simple password check first
  if (password !== '123456' && password !== 'password') {
    return {
      success: false,
      error: 'Mật khẩu không đúng. Thử: 123456 hoặc password',
    };
  }

  // Normalize email (convert to lowercase and check aliases)
  const normalizedEmail = email.toLowerCase();
  const actualEmail = emailAliases[normalizedEmail] || normalizedEmail;

  // Check if email is in the real user list or mock data
  const isRealUser = realUserEmails.includes(actualEmail) || realUserEmails.includes(normalizedEmail);

  // Try to find user in mock data first
  let user = mockUsers.find(u =>
    u.email.toLowerCase() === actualEmail ||
    u.email.toLowerCase() === normalizedEmail
  );

  // If not found in mock data but is a real user, create dynamic user
  if (!user && (isRealUser || realUserEmails.length > 0)) {
    // Map email to user info based on real data
    const userInfo = getUserInfoFromEmail(actualEmail);

    user = {
      id: `real_${Date.now()}`,
      name: userInfo.name,
      email: actualEmail,
      role: userInfo.role,
      team_id: userInfo.team_id,
      location: userInfo.location,
      department: 'Bán lẻ',
      department_type: 'retail',
      position: userInfo.position,
      status: 'active',
      password_changed: true,
    };

    console.log('Created dynamic user from real data:', user);
  }

  // If still not found, create generic user
  if (!user) {
    const emailPrefix = email.split('@')[0];
    const userName = emailPrefix.charAt(0).toUpperCase() + emailPrefix.slice(1);

    user = {
      id: `dynamic_${Date.now()}`,
      name: userName,
      email: email,
      role: 'employee',
      team_id: '1',
      location: 'Hà Nội',
      department: 'Bán lẻ',
      department_type: 'retail',
      position: 'Nhân viên',
      status: 'active',
      password_changed: true,
    };

    console.log('Created generic dynamic user:', user);
  }

  // Generate mock token
  const token = `mock_token_${user.id}_${Date.now()}`;

  return {
    success: true,
    data: {
      user,
      token,
    },
  };
};

// Helper function để map email thành user info
function getUserInfoFromEmail(email: string): {
  name: string;
  role: string;
  team_id: string;
  location: string;
  position: string;
} {
  const emailToUserMap: Record<string, any> = {
    '<EMAIL>': {
      name: 'Khổng Đức Mạnh',
      role: 'retail_director',
      team_id: '0',
      location: 'Hà Nội',
      position: 'Trưởng phòng'
    },
    '<EMAIL>': {
      name: 'Lương Việt Anh',
      role: 'team_leader',
      team_id: '1',
      location: 'Hà Nội',
      position: 'Trưởng nhóm'
    },
    '<EMAIL>': {
      name: 'Lê Khánh Duy',
      role: 'employee',
      team_id: '1',
      location: 'Hà Nội',
      position: 'Nhân viên'
    },
    '<EMAIL>': {
      name: 'Nguyễn Thị Thảo',
      role: 'team_leader',
      team_id: '2',
      location: 'Hà Nội',
      position: 'Trưởng nhóm'
    },
    '<EMAIL>': {
      name: 'Nguyễn Mạnh Linh',
      role: 'employee',
      team_id: '2',
      location: 'Hà Nội',
      position: 'Nhân viên'
    },
    '<EMAIL>': {
      name: 'Trịnh Thị Bốn',
      role: 'team_leader',
      team_id: '3',
      location: 'Hà Nội',
      position: 'Trưởng nhóm'
    },
    '<EMAIL>': {
      name: 'Phạm Thị Hương',
      role: 'team_leader',
      team_id: '4',
      location: 'Hà Nội',
      position: 'Trưởng nhóm'
    },
    '<EMAIL>': {
      name: 'Nguyễn Thị Nga',
      role: 'team_leader',
      team_id: '5',
      location: 'Hồ Chí Minh',
      position: 'Trưởng nhóm'
    },
    '<EMAIL>': {
      name: 'Hà Nguyễn Thanh Tuyền',
      role: 'employee',
      team_id: '5',
      location: 'Hồ Chí Minh',
      position: 'Nhân viên'
    },
    '<EMAIL>': {
      name: 'Nguyễn Ngọc Việt Khanh',
      role: 'team_leader',
      team_id: '6',
      location: 'Hồ Chí Minh',
      position: 'Trưởng nhóm'
    },
    '<EMAIL>': {
      name: 'Phùng Thị Thuỳ Vân',
      role: 'employee',
      team_id: '6',
      location: 'Hồ Chí Minh',
      position: 'Nhân viên'
    }
  };

  return emailToUserMap[email] || {
    name: email.split('@')[0],
    role: 'employee',
    team_id: '1',
    location: 'Hà Nội',
    position: 'Nhân viên'
  };
}

// Mock get users function - trả về tất cả users thật từ hệ thống
export const mockGetUsers = async (): Promise<{
  success: boolean;
  data?: MockUser[];
  error?: string;
}> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  // Tạo danh sách users thật từ hệ thống
  const realUsers: MockUser[] = [
    {
      id: '1',
      name: 'Khổng Đức Mạnh',
      email: '<EMAIL>',
      role: 'retail_director',
      team_id: '0', // Chưa có nhóm
      location: 'Hà Nội',
      department: 'Bán lẻ',
      department_type: 'retail',
      position: 'Trưởng phòng',
      status: 'active',
      password_changed: true,
    },
    {
      id: '2',
      name: 'Lương Việt Anh',
      email: '<EMAIL>',
      role: 'team_leader',
      team_id: '1',
      location: 'Hà Nội',
      department: 'Bán lẻ',
      department_type: 'retail',
      position: 'Trưởng nhóm',
      status: 'active',
      password_changed: true,
    },
    {
      id: '3',
      name: 'Lê Khánh Duy',
      email: '<EMAIL>',
      role: 'employee',
      team_id: '1',
      location: 'Hà Nội',
      department: 'Bán lẻ',
      department_type: 'retail',
      position: 'Nhân viên',
      status: 'active',
      password_changed: true,
    },
    {
      id: '4',
      name: 'Nguyễn Thị Thảo',
      email: '<EMAIL>',
      role: 'team_leader',
      team_id: '2',
      location: 'Hà Nội',
      department: 'Bán lẻ',
      department_type: 'retail',
      position: 'Trưởng nhóm',
      status: 'active',
      password_changed: true,
    },
    {
      id: '5',
      name: 'Nguyễn Mạnh Linh',
      email: '<EMAIL>',
      role: 'employee',
      team_id: '2',
      location: 'Hà Nội',
      department: 'Bán lẻ',
      department_type: 'retail',
      position: 'Nhân viên',
      status: 'active',
      password_changed: true,
    },
    {
      id: '6',
      name: 'Trịnh Thị Bốn',
      email: '<EMAIL>',
      role: 'team_leader',
      team_id: '3',
      location: 'Hà Nội',
      department: 'Bán lẻ',
      department_type: 'retail',
      position: 'Trưởng nhóm',
      status: 'active',
      password_changed: true,
    },
    {
      id: '7',
      name: 'Phạm Thị Hương',
      email: '<EMAIL>',
      role: 'team_leader',
      team_id: '4',
      location: 'Hà Nội',
      department: 'Bán lẻ',
      department_type: 'retail',
      position: 'Trưởng nhóm',
      status: 'active',
      password_changed: true,
    },
    {
      id: '8',
      name: 'Nguyễn Thị Nga',
      email: '<EMAIL>',
      role: 'team_leader',
      team_id: '5',
      location: 'Hồ Chí Minh',
      department: 'Bán lẻ',
      department_type: 'retail',
      position: 'Trưởng nhóm',
      status: 'active',
      password_changed: true,
    },
    {
      id: '9',
      name: 'Hà Nguyễn Thanh Tuyền',
      email: '<EMAIL>',
      role: 'employee',
      team_id: '5',
      location: 'Hồ Chí Minh',
      department: 'Bán lẻ',
      department_type: 'retail',
      position: 'Nhân viên',
      status: 'active',
      password_changed: true,
    },
    {
      id: '10',
      name: 'Nguyễn Ngọc Việt Khanh',
      email: '<EMAIL>',
      role: 'team_leader',
      team_id: '6',
      location: 'Hồ Chí Minh',
      department: 'Bán lẻ',
      department_type: 'retail',
      position: 'Trưởng nhóm',
      status: 'active',
      password_changed: true,
    },
    {
      id: '11',
      name: 'Phùng Thị Thuỳ Vân',
      email: '<EMAIL>',
      role: 'employee',
      team_id: '6',
      location: 'Hồ Chí Minh',
      department: 'Bán lẻ',
      department_type: 'retail',
      position: 'Nhân viên',
      status: 'active',
      password_changed: true,
    },
  ];

  return {
    success: true,
    data: realUsers,
  };
};

// Mock get teams function - trả về tất cả teams thật từ hệ thống
export const mockGetTeams = async (): Promise<{
  success: boolean;
  data?: MockTeam[];
  error?: string;
}> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  // Tạo danh sách teams thật từ hệ thống
  const realTeams: MockTeam[] = [
    {
      id: '1',
      name: 'NHÓM 1 - VIỆT ANH',
      leader_id: '2',
      location: 'Hà Nội',
      description: 'Nhóm bán lẻ do Lương Việt Anh làm trưởng nhóm',
      department: 'Bán lẻ',
      department_type: 'retail',
    },
    {
      id: '2',
      name: 'NHÓM 2 - THẢO',
      leader_id: '4',
      location: 'Hà Nội',
      description: 'Nhóm bán lẻ do Nguyễn Thị Thảo làm trưởng nhóm',
      department: 'Bán lẻ',
      department_type: 'retail',
    },
    {
      id: '3',
      name: 'NHÓM 3',
      leader_id: '6',
      location: 'Hà Nội',
      description: 'Nhóm bán lẻ do Trịnh Thị Bốn làm trưởng nhóm',
      department: 'Bán lẻ',
      department_type: 'retail',
    },
    {
      id: '4',
      name: 'NHÓM 4',
      leader_id: '7',
      location: 'Hà Nội',
      description: 'Nhóm bán lẻ do Phạm Thị Hương làm trưởng nhóm',
      department: 'Bán lẻ',
      department_type: 'retail',
    },
    {
      id: '5',
      name: 'NHÓM 1',
      leader_id: '8',
      location: 'Hồ Chí Minh',
      description: 'Nhóm bán lẻ HCM do Nguyễn Thị Nga làm trưởng nhóm',
      department: 'Bán lẻ',
      department_type: 'retail',
    },
    {
      id: '6',
      name: 'NHÓM 2',
      leader_id: '10',
      location: 'Hồ Chí Minh',
      description: 'Nhóm bán lẻ HCM do Nguyễn Ngọc Việt Khanh làm trưởng nhóm',
      department: 'Bán lẻ',
      department_type: 'retail',
    },
  ];

  return {
    success: true,
    data: realTeams,
  };
};

// Mock update user function
export const mockUpdateUser = async (id: string, userData: Partial<MockUser>): Promise<{
  success: boolean;
  data?: MockUser;
  error?: string;
}> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const userIndex = mockUsers.findIndex(u => u.id === id);
  if (userIndex === -1) {
    return {
      success: false,
      error: 'Không tìm thấy người dùng',
    };
  }

  // Update user data
  mockUsers[userIndex] = { ...mockUsers[userIndex], ...userData };
  
  return {
    success: true,
    data: mockUsers[userIndex],
  };
};
