import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';

import { useAuth } from '@/context/AuthContext';
import LoadingScreen from '@/components/ui/LoadingScreen';
import { useToast } from '@/hooks/use-toast';

interface ProtectedReportsRouteProps {
  children: React.ReactNode;
}

const ProtectedReportsRoute: React.FC<ProtectedReportsRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading, currentUser } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    // Hiển thị thông báo khi người dùng không có quyền truy cập
    if (!isLoading && isAuthenticated && currentUser?.role !== 'retail_director') {
      toast({
        title: 'Không có quyền truy cập',
        description: 'Chỉ Trưởng phòng Bán lẻ mới có thể xem báo cáo.',
        variant: 'destructive',
      });
    }
  }, [isLoading, isAuthenticated, currentUser, toast]);

  if (isLoading) {
    return <LoadingScreen message="Đang kiểm tra quyền truy cập..." />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Chỉ cho phép retail_director (Khổng Đức Mạnh) truy cập báo cáo
  if (currentUser?.role !== 'retail_director') {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

export default ProtectedReportsRoute;
