import { User } from '@/types/user';
import { reportsDataService } from '@/services/ReportsDataService';

export interface KpiItem {
  title: string;
  value: string;
  oldValue: string;
  change: number;
  data: Array<{ value: number }>;
}

export const getKpiDataForUser = (currentUser: User | null): KpiItem[] => {
  // Sử dụng ReportsDataService để lấy dữ liệu đồng bộ
  return reportsDataService.getKpiDataForUser(currentUser);
};

// Xây dựng tiêu đề phụ (subtitle) dựa trên loại người dùng
export const getDashboardSubtitle = (currentUser: User | null): string => {
  const isDirector =
    currentUser?.role === 'retail_director' || currentUser?.role === 'project_director';
  const isTeamLeader = currentUser?.role === 'team_leader';

  if (isDirector) {
    const departmentType =
      currentUser?.department_type === 'retail' ? 'Kinh doanh bán lẻ' : 'Kinh doanh dự án';
    return `Tổng quan về hiệu suất kinh doanh phòng ${departmentType}`;
  } else if (isTeamLeader) {
    return 'Tổng quan về hiệu suất kinh doanh của nhóm của bạn';
  } else {
    return 'Tổng quan về hiệu suất kinh doanh cá nhân của bạn';
  }
};
